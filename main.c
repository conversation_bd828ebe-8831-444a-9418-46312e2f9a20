#include "src/voxforge.h"

// 声明外部函数
void free_config(VoxForgeConfig* config);

int main(int argc, char* argv[]) {
    VoxForgeConfig config;
    VoxForgeSession session;
    VoxForgeError error;

    printf("VoxForge - 声音锻造厂\n");
    printf("Version: 0.1.0 (Debug)\n\n");

    // 解析命令行参数
    error = parse_arguments(argc, argv, &config);
    if (error != VOXFORGE_SUCCESS) {
        if (error != VOXFORGE_ERROR_INVALID_ARGS) {
            print_error(error);
        }
        return error;
    }

    // 初始化 ONNX Runtime
    error = voxforge_init(&session);
    if (error != VOXFORGE_SUCCESS) {
        print_error(error);
        free_config(&config);
        return error;
    }

    // 加载模型
    error = voxforge_load_model(&session, config.model_path);
    if (error != VOXFORGE_SUCCESS) {
        print_error(error);
        voxforge_cleanup(&session);
        free_config(&config);
        return error;
    }

    // 执行推理
    error = voxforge_inference(&session, &config);
    if (error != VOXFORGE_SUCCESS) {
        print_error(error);
        voxforge_cleanup(&session);
        free_config(&config);
        return error;
    }

    printf("\nSynthesis completed successfully!\n");

    // 清理资源
    voxforge_cleanup(&session);
    free_config(&config);

    return 0;
}