# 使用 Ubuntu 作为基础镜像进行静态编译
FROM ubuntu:22.04

# 设置环境变量
ENV DEBIAN_FRONTEND=noninteractive
ENV CMAKE_BUILD_TYPE=Release
ENV STATIC_BUILD=ON

# 安装构建依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    cmake \
    wget \
    curl \
    unzip \
    git \
    pkg-config \
    && rm -rf /var/lib/apt/lists/*

# 创建工作目录
WORKDIR /app

# 复制源代码
COPY . .

# 下载 ONNX Runtime for Linux
RUN mkdir -p third_party && \
    cd third_party && \
    wget https://github.com/microsoft/onnxruntime/releases/download/v1.16.3/onnxruntime-linux-x64-1.16.3.tgz && \
    tar -xzf onnxruntime-linux-x64-1.16.3.tgz && \
    rm onnxruntime-linux-x64-1.16.3.tgz

# 构建项目
RUN mkdir -p build && \
    cd build && \
    cmake -DCMAKE_BUILD_TYPE=Release -DSTATIC_BUILD=ON .. && \
    make -j$(nproc)

# 验证静态链接
RUN ldd build/VoxForge || echo "Static binary - no dynamic dependencies"

# 创建输出目录
RUN mkdir -p /output && \
    cp build/VoxForge /output/

# 设置入口点
ENTRYPOINT ["/output/VoxForge"]
