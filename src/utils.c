#include "voxforge.h"
#include <getopt.h>
#include <sys/stat.h>

void print_usage(const char* program_name) {
    printf("VoxForge - 声音锻造厂\n");
    printf("Usage: %s [OPTIONS] \"text to synthesize\"\n\n", program_name);
    printf("Options:\n");
    printf("  --vits-model=PATH        Path to VITS ONNX model file\n");
    printf("  --vits-dict-dir=PATH     Path to dictionary directory\n");
    printf("  --vits-lexicon=PATH      Path to lexicon file\n");
    printf("  --vits-tokens=PATH       Path to tokens file\n");
    printf("  --sid=ID                 Speaker ID (default: 0)\n");
    printf("  --output-filename=PATH   Output audio file path (default: output.wav)\n");
    printf("  --noise-scale=FLOAT      Noise scale (default: 0.667, lower = less harsh)\n");
    printf("  --noise-scale-w=FLOAT    Noise scale W (default: 0.8, lower = softer)\n");
    printf("  --length-scale=FLOAT     Length scale (default: 1.0, higher = slower)\n");
    printf("  -v, --verbose            Enable verbose output\n");
    printf("  -h, --help               Show this help message\n\n");
    printf("Example:\n");
    printf("  %s --vits-model=model.onnx --sid=2 --output-filename=test.wav \"Hello World\"\n", program_name);
}

void print_error(VoxForgeError error) {
    switch (error) {
        case VOXFORGE_SUCCESS:
            break;
        case VOXFORGE_ERROR_INVALID_ARGS:
            fprintf(stderr, "Error: Invalid arguments\n");
            break;
        case VOXFORGE_ERROR_FILE_NOT_FOUND:
            fprintf(stderr, "Error: File not found\n");
            break;
        case VOXFORGE_ERROR_ONNX_INIT:
            fprintf(stderr, "Error: Failed to initialize ONNX Runtime\n");
            break;
        case VOXFORGE_ERROR_ONNX_SESSION:
            fprintf(stderr, "Error: ONNX session error\n");
            break;
        case VOXFORGE_ERROR_ONNX_INFERENCE:
            fprintf(stderr, "Error: ONNX inference failed\n");
            break;
        case VOXFORGE_ERROR_MEMORY:
            fprintf(stderr, "Error: Memory allocation failed\n");
            break;
        default:
            fprintf(stderr, "Error: Unknown error (%d)\n", error);
            break;
    }
}

bool file_exists(const char* path) {
    if (!path) {
        return false;
    }
    
    struct stat st;
    return stat(path, &st) == 0;
}

VoxForgeError parse_arguments(int argc, char* argv[], VoxForgeConfig* config) {
    if (!config) {
        return VOXFORGE_ERROR_INVALID_ARGS;
    }
    
    // 初始化配置
    memset(config, 0, sizeof(VoxForgeConfig));
    config->speaker_id = 0;
    config->output_path = strdup("output.wav");

    // 初始化TTS参数默认值
    config->tts_params.noise_scale = 0.667f;
    config->tts_params.noise_scale_w = 0.8f;
    config->tts_params.length_scale = 1.0f;
    
    static struct option long_options[] = {
        {"vits-model", required_argument, 0, 'm'},
        {"vits-dict-dir", required_argument, 0, 'd'},
        {"vits-lexicon", required_argument, 0, 'l'},
        {"vits-tokens", required_argument, 0, 't'},
        {"sid", required_argument, 0, 's'},
        {"output-filename", required_argument, 0, 'o'},
        {"noise-scale", required_argument, 0, 'n'},
        {"noise-scale-w", required_argument, 0, 'w'},
        {"length-scale", required_argument, 0, 'L'},
        {"verbose", no_argument, 0, 'v'},
        {"help", no_argument, 0, 'h'},
        {0, 0, 0, 0}
    };
    
    int option_index = 0;
    int c;
    
    while ((c = getopt_long(argc, argv, "m:d:l:t:s:o:n:w:L:vh", long_options, &option_index)) != -1) {
        switch (c) {
            case 'm':
                config->model_path = strdup(optarg);
                break;
            case 'd':
                config->dict_dir = strdup(optarg);
                break;
            case 'l':
                config->lexicon_path = strdup(optarg);
                break;
            case 't':
                config->tokens_path = strdup(optarg);
                break;
            case 's':
                config->speaker_id = atoi(optarg);
                break;
            case 'o':
                free(config->output_path);
                config->output_path = strdup(optarg);
                break;
            case 'n':
                config->tts_params.noise_scale = atof(optarg);
                break;
            case 'w':
                config->tts_params.noise_scale_w = atof(optarg);
                break;
            case 'L':
                config->tts_params.length_scale = atof(optarg);
                break;
            case 'v':
                config->verbose = true;
                break;
            case 'h':
                print_usage(argv[0]);
                return VOXFORGE_ERROR_INVALID_ARGS;
            case '?':
                return VOXFORGE_ERROR_INVALID_ARGS;
            default:
                return VOXFORGE_ERROR_INVALID_ARGS;
        }
    }
    
    // 获取要合成的文本
    if (optind < argc) {
        config->text = strdup(argv[optind]);
    } else {
        fprintf(stderr, "Error: No text provided for synthesis\n");
        return VOXFORGE_ERROR_INVALID_ARGS;
    }
    
    // 验证必需的参数
    if (!config->model_path) {
        fprintf(stderr, "Error: --vits-model is required\n");
        return VOXFORGE_ERROR_INVALID_ARGS;
    }
    
    if (config->verbose) {
        printf("Configuration:\n");
        printf("  Model: %s\n", config->model_path);
        printf("  Dict dir: %s\n", config->dict_dir ? config->dict_dir : "Not specified");
        printf("  Lexicon: %s\n", config->lexicon_path ? config->lexicon_path : "Not specified");
        printf("  Tokens: %s\n", config->tokens_path ? config->tokens_path : "Not specified");
        printf("  Speaker ID: %d\n", config->speaker_id);
        printf("  Output: %s\n", config->output_path);
        printf("  Text: %s\n", config->text);
    }
    
    return VOXFORGE_SUCCESS;
}

void free_config(VoxForgeConfig* config) {
    if (!config) {
        return;
    }
    
    free(config->model_path);
    free(config->dict_dir);
    free(config->lexicon_path);
    free(config->tokens_path);
    free(config->output_path);
    free(config->text);
    
    memset(config, 0, sizeof(VoxForgeConfig));
}
