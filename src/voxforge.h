#ifndef VOXFORGE_H
#define VOXFORGE_H

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdbool.h>

#ifdef HAVE_ONNX_RUNTIME
#include <onnxruntime_c_api.h>
#endif

// 错误码定义
typedef enum {
    VOXFORGE_SUCCESS = 0,
    VOXFORGE_ERROR_INVALID_ARGS = -1,
    VOXFORGE_ERROR_FILE_NOT_FOUND = -2,
    VOXFORGE_ERROR_ONNX_INIT = -3,
    VOXFORGE_ERROR_ONNX_SESSION = -4,
    VOXFORGE_ERROR_ONNX_INFERENCE = -5,
    VOXFORGE_ERROR_MEMORY = -6
} VoxForgeError;

// 配置结构体
typedef struct {
    char* model_path;
    char* dict_dir;
    char* lexicon_path;
    char* tokens_path;
    char* output_path;
    int speaker_id;
    char* text;
    bool verbose;
} VoxForgeConfig;

// ONNX 会话结构体
typedef struct {
#ifdef HAVE_ONNX_RUNTIME
    const OrtApi* ort_api;
    OrtEnv* env;
    OrtSession* session;
    OrtSessionOptions* session_options;
    OrtMemoryInfo* memory_info;
#endif
    bool initialized;
} VoxForgeSession;

// 函数声明
VoxForgeError voxforge_init(VoxForgeSession* session);
VoxForgeError voxforge_load_model(VoxForgeSession* session, const char* model_path);
VoxForgeError voxforge_inference(VoxForgeSession* session, const VoxForgeConfig* config);
void voxforge_cleanup(VoxForgeSession* session);

// 工具函数
VoxForgeError parse_arguments(int argc, char* argv[], VoxForgeConfig* config);
void print_usage(const char* program_name);
void print_error(VoxForgeError error);
bool file_exists(const char* path);
void free_config(VoxForgeConfig* config);

#endif // VOXFORGE_H
