#include "voxforge.h"
#include <string.h>

// 全局变量存储词典和tokens
static char lexicon_entries[66376][32];  // 中文字符
static char lexicon_phonemes[66376][32]; // 对应拼音
static size_t lexicon_count = 0;

static char token_phonemes[2070][32];    // 音素字符串
static int token_ids[2070];              // 对应ID
static size_t token_count = 0;

VoxForgeError load_lexicon(const char* lexicon_path) {
    if (!lexicon_path) {
        return VOXFORGE_ERROR_INVALID_ARGS;
    }
    
    FILE* file = fopen(lexicon_path, "r");
    if (!file) {
        fprintf(stderr, "Failed to open lexicon file: %s\n", lexicon_path);
        return VOXFORGE_ERROR_FILE_NOT_FOUND;
    }
    
    char line[256];
    lexicon_count = 0;
    
    printf("Loading lexicon from %s...\n", lexicon_path);
    
    while (fgets(line, sizeof(line), file) && lexicon_count < 66376) {
        // 移除换行符
        line[strcspn(line, "\n")] = 0;
        
        // 解析格式: "字符 注音符号"
        char* space_pos = strchr(line, ' ');
        if (space_pos) {
            *space_pos = 0; // 分割字符串

            strncpy(lexicon_entries[lexicon_count], line, 31);
            lexicon_entries[lexicon_count][31] = 0;

            strncpy(lexicon_phonemes[lexicon_count], space_pos + 1, 31);
            lexicon_phonemes[lexicon_count][31] = 0;

            lexicon_count++;
        }
    }
    
    fclose(file);
    printf("Loaded %zu lexicon entries\n", lexicon_count);
    return VOXFORGE_SUCCESS;
}

VoxForgeError load_tokens(const char* tokens_path) {
    if (!tokens_path) {
        return VOXFORGE_ERROR_INVALID_ARGS;
    }
    
    FILE* file = fopen(tokens_path, "r");
    if (!file) {
        fprintf(stderr, "Failed to open tokens file: %s\n", tokens_path);
        return VOXFORGE_ERROR_FILE_NOT_FOUND;
    }
    
    char line[256];
    token_count = 0;
    
    printf("Loading tokens from %s...\n", tokens_path);
    
    while (fgets(line, sizeof(line), file) && token_count < 2070) {
        // 移除换行符
        line[strcspn(line, "\n")] = 0;
        
        // 解析格式: "音素 ID"
        char* space_pos = strchr(line, ' ');
        if (space_pos) {
            *space_pos = 0; // 分割字符串
            
            strncpy(token_phonemes[token_count], line, 31);
            token_phonemes[token_count][31] = 0;
            
            token_ids[token_count] = atoi(space_pos + 1);
            token_count++;
        }
    }
    
    fclose(file);
    printf("Loaded %zu token mappings\n", token_count);
    return VOXFORGE_SUCCESS;
}

// 查找中文字符对应的拼音
const char* find_phoneme_for_char(const char* utf8_char) {
    for (size_t i = 0; i < lexicon_count; i++) {
        if (strcmp(lexicon_entries[i], utf8_char) == 0) {
            return lexicon_phonemes[i];
        }
    }
    return NULL;
}

// 查找拼音对应的token ID
int find_token_id(const char* phoneme) {
    for (size_t i = 0; i < token_count; i++) {
        if (strcmp(token_phonemes[i], phoneme) == 0) {
            return token_ids[i];
        }
    }
    return -1; // 未找到
}

VoxForgeError text_to_phoneme_ids(const char* text, int64_t** phoneme_ids, size_t* phoneme_count) {
    if (!text || !phoneme_ids || !phoneme_count) {
        return VOXFORGE_ERROR_INVALID_ARGS;
    }

    if (lexicon_count == 0 || token_count == 0) {
        fprintf(stderr, "Lexicon or tokens not loaded\n");
        return VOXFORGE_ERROR_INVALID_ARGS;
    }

    printf("Converting text to phoneme IDs: \"%s\"\n", text);

    // 分配临时数组
    int64_t* temp_ids = malloc(strlen(text) * 20 * sizeof(int64_t)); // 预留足够空间
    if (!temp_ids) {
        return VOXFORGE_ERROR_MEMORY;
    }

    size_t count = 0;
    size_t text_len = strlen(text);
    size_t i = 0;

    // 不添加BOS/EOS token，直接处理文本

    while (i < text_len) {
        // 处理UTF-8中文字符（3字节）
        if ((unsigned char)text[i] >= 0xE0) {
            char utf8_char[4] = {0};
            utf8_char[0] = text[i];
            utf8_char[1] = text[i+1];
            utf8_char[2] = text[i+2];

            const char* phoneme_str = find_phoneme_for_char(utf8_char);
            if (phoneme_str) {
                printf("'%s' -> '%s'\n", utf8_char, phoneme_str);
                // 解析注音符号序列，每个符号单独查找token ID
                char* phoneme_copy = strdup(phoneme_str);
                char* token = strtok(phoneme_copy, " ");
                while (token != NULL) {
                    int token_id = find_token_id(token);
                    if (token_id >= 0) {
                        temp_ids[count++] = token_id;
                        printf("  '%s' -> %d\n", token, token_id);
                    } else {
                        printf("  Warning: phoneme '%s' not found in tokens\n", token);
                        temp_ids[count++] = 0; // 使用_作为默认
                    }
                    token = strtok(NULL, " ");
                }

                free(phoneme_copy);
            } else {
                printf("Warning: character '%s' not found in lexicon\n", utf8_char);
                temp_ids[count++] = 0; // 使用_作为默认
            }
            i += 3;
        }
        // 处理ASCII字符
        else if ((unsigned char)text[i] < 0x80) {
            // 处理标点符号
            if (text[i] == ' ') {
                temp_ids[count++] = 49; // 空格
            } else if (text[i] == ',') {
                temp_ids[count++] = 1; // ，
            } else if (text[i] == '.') {
                temp_ids[count++] = 2; // 。
            } else if (text[i] == '!') {
                temp_ids[count++] = 3; // ！
            } else if (text[i] == '?') {
                temp_ids[count++] = 4; // ？
            } else {
                temp_ids[count++] = 0; // 默认_
            }
            i++;
        }
        // 跳过其他字节
        else {
            i++;
        }
    }
    // 分配最终数组
    *phoneme_ids = malloc(count * sizeof(int64_t));
    if (!*phoneme_ids) {
        free(temp_ids);
        return VOXFORGE_ERROR_MEMORY;
    }
    memcpy(*phoneme_ids, temp_ids, count * sizeof(int64_t));
    *phoneme_count = count;
    free(temp_ids);
    printf("Generated %zu phoneme IDs\n", count);
    return VOXFORGE_SUCCESS;
}

void cleanup_text_resources(void) {
    lexicon_count = 0;
    token_count = 0;
    printf("Text processing resources cleaned up\n");
}
