#include "voxforge.h"

VoxForgeError voxforge_init(VoxForgeSession* session) {
    if (!session) {
        return VOXFORGE_ERROR_INVALID_ARGS;
    }
    
    memset(session, 0, sizeof(VoxForgeSession));
    
#ifdef HAVE_ONNX_RUNTIME
    session->ort_api = OrtGetApiBase()->GetApi(ORT_API_VERSION);
    if (!session->ort_api) {
        fprintf(stderr, "Failed to get ONNX Runtime API\n");
        return VOXFORGE_ERROR_ONNX_INIT;
    }
    
    // 创建环境
    OrtStatus* status = session->ort_api->CreateEnv(ORT_LOGGING_LEVEL_WARNING, "VoxForge", &session->env);
    if (status != NULL) {
        fprintf(stderr, "Failed to create ONNX Runtime environment\n");
        session->ort_api->ReleaseStatus(status);
        return VOXFORGE_ERROR_ONNX_INIT;
    }
    
    // 创建会话选项
    status = session->ort_api->CreateSessionOptions(&session->session_options);
    if (status != NULL) {
        fprintf(stderr, "Failed to create session options\n");
        session->ort_api->ReleaseStatus(status);
        return VOXFORGE_ERROR_ONNX_INIT;
    }
    
    // 设置线程数
    session->ort_api->SetIntraOpNumThreads(session->session_options, 1);
    session->ort_api->SetInterOpNumThreads(session->session_options, 1);
    
    // 创建内存信息
    status = session->ort_api->CreateCpuMemoryInfo(OrtArenaAllocator, OrtMemTypeDefault, &session->memory_info);
    if (status != NULL) {
        fprintf(stderr, "Failed to create memory info\n");
        session->ort_api->ReleaseStatus(status);
        return VOXFORGE_ERROR_ONNX_INIT;
    }
    
    session->initialized = true;
    printf("ONNX Runtime initialized successfully\n");
    return VOXFORGE_SUCCESS;
#else
    printf("ONNX Runtime not available - running in debug mode\n");
    session->initialized = true;
    return VOXFORGE_SUCCESS;
#endif
}

VoxForgeError voxforge_load_model(VoxForgeSession* session, const char* model_path) {
    if (!session || !model_path) {
        return VOXFORGE_ERROR_INVALID_ARGS;
    }
    
    if (!session->initialized) {
        return VOXFORGE_ERROR_ONNX_INIT;
    }
    
    if (!file_exists(model_path)) {
        fprintf(stderr, "Model file not found: %s\n", model_path);
        return VOXFORGE_ERROR_FILE_NOT_FOUND;
    }
    
#ifdef HAVE_ONNX_RUNTIME
    // 加载模型
    OrtStatus* status = session->ort_api->CreateSession(session->env, model_path, session->session_options, &session->session);
    if (status != NULL) {
        fprintf(stderr, "Failed to load model: %s\n", model_path);
        session->ort_api->ReleaseStatus(status);
        return VOXFORGE_ERROR_ONNX_SESSION;
    }
    
    printf("Model loaded successfully: %s\n", model_path);
    return VOXFORGE_SUCCESS;
#else
    printf("Debug mode: Would load model %s\n", model_path);
    return VOXFORGE_SUCCESS;
#endif
}

VoxForgeError voxforge_inference(VoxForgeSession* session, const VoxForgeConfig* config) {
    if (!session || !config) {
        return VOXFORGE_ERROR_INVALID_ARGS;
    }
    
    if (!session->initialized) {
        return VOXFORGE_ERROR_ONNX_INIT;
    }
    
    printf("Starting inference...\n");
    printf("Text: %s\n", config->text ? config->text : "No text provided");
    printf("Speaker ID: %d\n", config->speaker_id);
    printf("Output file: %s\n", config->output_path ? config->output_path : "output.wav");
    
#ifdef HAVE_ONNX_RUNTIME
    if (!session->session) {
        fprintf(stderr, "No model loaded\n");
        return VOXFORGE_ERROR_ONNX_SESSION;
    }
    
    // 这里应该实现实际的推理逻辑
    // 由于 VITS 模型的输入输出比较复杂，这里先做一个简单的示例
    printf("ONNX Runtime inference would be performed here\n");
    printf("Model inputs would be prepared from text and speaker_id\n");
    printf("Audio output would be generated and saved to %s\n", config->output_path);
    
    return VOXFORGE_SUCCESS;
#else
    printf("Debug mode: Simulating inference\n");
    printf("Would process text: '%s'\n", config->text);
    printf("Would use speaker ID: %d\n", config->speaker_id);
    printf("Would save audio to: %s\n", config->output_path);
    return VOXFORGE_SUCCESS;
#endif
}

void voxforge_cleanup(VoxForgeSession* session) {
    if (!session) {
        return;
    }
    
#ifdef HAVE_ONNX_RUNTIME
    if (session->session) {
        session->ort_api->ReleaseSession(session->session);
        session->session = NULL;
    }
    
    if (session->memory_info) {
        session->ort_api->ReleaseMemoryInfo(session->memory_info);
        session->memory_info = NULL;
    }
    
    if (session->session_options) {
        session->ort_api->ReleaseSessionOptions(session->session_options);
        session->session_options = NULL;
    }
    
    if (session->env) {
        session->ort_api->ReleaseEnv(session->env);
        session->env = NULL;
    }
#endif
    
    session->initialized = false;
    printf("VoxForge session cleaned up\n");
}
