#include "voxforge.h"

VoxForgeError voxforge_init(VoxForgeSession* session) {
    if (!session) {
        return VOXFORGE_ERROR_INVALID_ARGS;
    }
    
    memset(session, 0, sizeof(VoxForgeSession));
    
#ifdef HAVE_ONNX_RUNTIME
    session->ort_api = OrtGetApiBase()->GetApi(ORT_API_VERSION);
    if (!session->ort_api) {
        fprintf(stderr, "Failed to get ONNX Runtime API\n");
        return VOXFORGE_ERROR_ONNX_INIT;
    }
    
    // 创建环境
    OrtStatus* status = session->ort_api->CreateEnv(ORT_LOGGING_LEVEL_WARNING, "VoxForge", &session->env);
    if (status != NULL) {
        fprintf(stderr, "Failed to create ONNX Runtime environment\n");
        session->ort_api->ReleaseStatus(status);
        return VOXFORGE_ERROR_ONNX_INIT;
    }
    
    // 创建会话选项
    status = session->ort_api->CreateSessionOptions(&session->session_options);
    if (status != NULL) {
        fprintf(stderr, "Failed to create session options\n");
        session->ort_api->ReleaseStatus(status);
        return VOXFORGE_ERROR_ONNX_INIT;
    }
    
    // 设置线程数
    session->ort_api->SetIntraOpNumThreads(session->session_options, 1);
    session->ort_api->SetInterOpNumThreads(session->session_options, 1);
    
    // 创建内存信息
    status = session->ort_api->CreateCpuMemoryInfo(OrtArenaAllocator, OrtMemTypeDefault, &session->memory_info);
    if (status != NULL) {
        fprintf(stderr, "Failed to create memory info\n");
        session->ort_api->ReleaseStatus(status);
        return VOXFORGE_ERROR_ONNX_INIT;
    }
    
    session->initialized = true;
    printf("ONNX Runtime initialized successfully\n");
    return VOXFORGE_SUCCESS;
#else
    printf("ONNX Runtime not available - running in debug mode\n");
    session->initialized = true;
    return VOXFORGE_SUCCESS;
#endif
}

VoxForgeError voxforge_load_model(VoxForgeSession* session, const char* model_path) {
    if (!session || !model_path) {
        return VOXFORGE_ERROR_INVALID_ARGS;
    }
    
    if (!session->initialized) {
        return VOXFORGE_ERROR_ONNX_INIT;
    }
    
    if (!file_exists(model_path)) {
        fprintf(stderr, "Model file not found: %s\n", model_path);
        return VOXFORGE_ERROR_FILE_NOT_FOUND;
    }
    
#ifdef HAVE_ONNX_RUNTIME
    // 加载模型
    OrtStatus* status = session->ort_api->CreateSession(session->env, model_path, session->session_options, &session->session);
    if (status != NULL) {
        fprintf(stderr, "Failed to load model: %s\n", model_path);
        session->ort_api->ReleaseStatus(status);
        return VOXFORGE_ERROR_ONNX_SESSION;
    }
    
    printf("Model loaded successfully: %s\n", model_path);
    return VOXFORGE_SUCCESS;
#else
    printf("Debug mode: Would load model %s\n", model_path);
    return VOXFORGE_SUCCESS;
#endif
}

VoxForgeError voxforge_inference(VoxForgeSession* session, const VoxForgeConfig* config) {
    if (!session || !config) {
        return VOXFORGE_ERROR_INVALID_ARGS;
    }
    
    if (!session->initialized) {
        return VOXFORGE_ERROR_ONNX_INIT;
    }
    
    printf("Starting inference...\n");
    printf("Text: %s\n", config->text ? config->text : "No text provided");
    printf("Speaker ID: %d\n", config->speaker_id);
    printf("Output file: %s\n", config->output_path ? config->output_path : "output.wav");
    
#ifdef HAVE_ONNX_RUNTIME
    if (!session->session) {
        fprintf(stderr, "No model loaded\n");
        return VOXFORGE_ERROR_ONNX_SESSION;
    }
    
    // 实现真正的ONNX推理
    printf("Preparing model inputs...\n");

    // 简化的文本处理：将中文文本转换为音素序列
    // 这里我们创建一个简单的音素序列作为示例
    int64_t text_tokens[] = {1, 23, 45, 67, 89, 12, 34, 56, 78, 90, 2}; // 示例音素序列
    size_t text_length = sizeof(text_tokens) / sizeof(text_tokens[0]);

    // 创建输入张量
    const char* input_names[] = {"text", "text_lengths"};
    const char* output_names[] = {"audio"};

    // 文本输入张量
    int64_t text_shape[] = {1, (int64_t)text_length};
    OrtValue* text_tensor = NULL;
    OrtStatus* status = session->ort_api->CreateTensorWithDataAsOrtValue(
        session->memory_info,
        text_tokens,
        text_length * sizeof(int64_t),
        text_shape,
        2,
        ONNX_TENSOR_ELEMENT_DATA_TYPE_INT64,
        &text_tensor
    );

    if (status != NULL) {
        fprintf(stderr, "Failed to create text tensor\n");
        session->ort_api->ReleaseStatus(status);
        return VOXFORGE_ERROR_ONNX_INFERENCE;
    }

    // 文本长度张量
    int64_t text_len_value = (int64_t)text_length;
    int64_t text_len_shape[] = {1};
    OrtValue* text_len_tensor = NULL;
    status = session->ort_api->CreateTensorWithDataAsOrtValue(
        session->memory_info,
        &text_len_value,
        sizeof(int64_t),
        text_len_shape,
        1,
        ONNX_TENSOR_ELEMENT_DATA_TYPE_INT64,
        &text_len_tensor
    );

    if (status != NULL) {
        fprintf(stderr, "Failed to create text length tensor\n");
        session->ort_api->ReleaseStatus(status);
        session->ort_api->ReleaseValue(text_tensor);
        return VOXFORGE_ERROR_ONNX_INFERENCE;
    }

    // 准备输入和输出
    const OrtValue* inputs[] = {text_tensor, text_len_tensor};
    OrtValue* outputs[] = {NULL};

    printf("Running ONNX inference...\n");

    // 执行推理
    status = session->ort_api->Run(
        session->session,
        NULL,  // run options
        input_names,
        inputs,
        2,  // 输入数量
        output_names,
        1,  // 输出数量
        outputs
    );

    if (status != NULL) {
        fprintf(stderr, "ONNX inference failed\n");
        session->ort_api->ReleaseStatus(status);
        session->ort_api->ReleaseValue(text_tensor);
        session->ort_api->ReleaseValue(text_len_tensor);
        return VOXFORGE_ERROR_ONNX_INFERENCE;
    }

    printf("Inference completed successfully!\n");

    // 获取输出数据
    float* audio_data = NULL;
    status = session->ort_api->GetTensorMutableData(outputs[0], (void**)&audio_data);
    if (status != NULL) {
        fprintf(stderr, "Failed to get output data\n");
        session->ort_api->ReleaseStatus(status);
    } else {
        // 获取输出形状信息
        OrtTensorTypeAndShapeInfo* shape_info;
        session->ort_api->GetTensorTypeAndShape(outputs[0], &shape_info);

        size_t dim_count;
        session->ort_api->GetDimensionsCount(shape_info, &dim_count);

        int64_t* dims = malloc(dim_count * sizeof(int64_t));
        session->ort_api->GetDimensions(shape_info, dims, dim_count);

        size_t audio_length = 1;
        for (size_t i = 0; i < dim_count; i++) {
            audio_length *= dims[i];
        }

        printf("Generated audio: %zu samples\n", audio_length);

        // 保存音频到文件
        if (save_audio_to_file(audio_data, audio_length, config->output_path) == VOXFORGE_SUCCESS) {
            printf("Audio saved to: %s\n", config->output_path);
        } else {
            fprintf(stderr, "Failed to save audio file\n");
        }

        free(dims);
        session->ort_api->ReleaseTensorTypeAndShapeInfo(shape_info);
    }

    // 清理资源
    session->ort_api->ReleaseValue(text_tensor);
    session->ort_api->ReleaseValue(text_len_tensor);
    if (outputs[0]) {
        session->ort_api->ReleaseValue(outputs[0]);
    }

    return VOXFORGE_SUCCESS;
#else
    printf("Debug mode: Simulating inference\n");
    printf("Would process text: '%s'\n", config->text);
    printf("Would use speaker ID: %d\n", config->speaker_id);
    printf("Would save audio to: %s\n", config->output_path);
    return VOXFORGE_SUCCESS;
#endif
}

void voxforge_cleanup(VoxForgeSession* session) {
    if (!session) {
        return;
    }
    
#ifdef HAVE_ONNX_RUNTIME
    if (session->session) {
        session->ort_api->ReleaseSession(session->session);
        session->session = NULL;
    }
    
    if (session->memory_info) {
        session->ort_api->ReleaseMemoryInfo(session->memory_info);
        session->memory_info = NULL;
    }
    
    if (session->session_options) {
        session->ort_api->ReleaseSessionOptions(session->session_options);
        session->session_options = NULL;
    }
    
    if (session->env) {
        session->ort_api->ReleaseEnv(session->env);
        session->env = NULL;
    }
#endif
    
    session->initialized = false;
    printf("VoxForge session cleaned up\n");
}
