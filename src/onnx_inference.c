#include "voxforge.h"
#include <math.h>
#include <time.h>

VoxForgeError voxforge_init(VoxForgeSession* session) {
    if (!session) {
        return VOXFORGE_ERROR_INVALID_ARGS;
    }
    
    memset(session, 0, sizeof(VoxForgeSession));
    
#ifdef HAVE_ONNX_RUNTIME
    session->ort_api = OrtGetApiBase()->GetApi(ORT_API_VERSION);
    if (!session->ort_api) {
        fprintf(stderr, "Failed to get ONNX Runtime API\n");
        return VOXFORGE_ERROR_ONNX_INIT;
    }
    
    // 创建环境
    OrtStatus* status = session->ort_api->CreateEnv(ORT_LOGGING_LEVEL_WARNING, "VoxForge", &session->env);
    if (status != NULL) {
        fprintf(stderr, "Failed to create ONNX Runtime environment\n");
        session->ort_api->ReleaseStatus(status);
        return VOXFORGE_ERROR_ONNX_INIT;
    }
    
    // 创建会话选项
    status = session->ort_api->CreateSessionOptions(&session->session_options);
    if (status != NULL) {
        fprintf(stderr, "Failed to create session options\n");
        session->ort_api->ReleaseStatus(status);
        return VOXFORGE_ERROR_ONNX_INIT;
    }
    
    // 设置线程数
    session->ort_api->SetIntraOpNumThreads(session->session_options, 1);
    session->ort_api->SetInterOpNumThreads(session->session_options, 1);
    
    // 创建内存信息
    status = session->ort_api->CreateCpuMemoryInfo(OrtArenaAllocator, OrtMemTypeDefault, &session->memory_info);
    if (status != NULL) {
        fprintf(stderr, "Failed to create memory info\n");
        session->ort_api->ReleaseStatus(status);
        return VOXFORGE_ERROR_ONNX_INIT;
    }
    
    session->initialized = true;
    printf("ONNX Runtime initialized successfully\n");
    return VOXFORGE_SUCCESS;
#else
    printf("ONNX Runtime not available - running in debug mode\n");
    session->initialized = true;
    return VOXFORGE_SUCCESS;
#endif
}

VoxForgeError voxforge_load_model(VoxForgeSession* session, const char* model_path) {
    if (!session || !model_path) {
        return VOXFORGE_ERROR_INVALID_ARGS;
    }
    
    if (!session->initialized) {
        return VOXFORGE_ERROR_ONNX_INIT;
    }
    
    if (!file_exists(model_path)) {
        fprintf(stderr, "Model file not found: %s\n", model_path);
        return VOXFORGE_ERROR_FILE_NOT_FOUND;
    }
    
#ifdef HAVE_ONNX_RUNTIME
    // 加载模型
    OrtStatus* status = session->ort_api->CreateSession(session->env, model_path, session->session_options, &session->session);
    if (status != NULL) {
        fprintf(stderr, "Failed to load model: %s\n", model_path);
        session->ort_api->ReleaseStatus(status);
        return VOXFORGE_ERROR_ONNX_SESSION;
    }
    
    printf("Model loaded successfully: %s\n", model_path);
    return VOXFORGE_SUCCESS;
#else
    printf("Debug mode: Would load model %s\n", model_path);
    return VOXFORGE_SUCCESS;
#endif
}

VoxForgeError voxforge_inference(VoxForgeSession* session, const VoxForgeConfig* config) {
    if (!session || !config) {
        return VOXFORGE_ERROR_INVALID_ARGS;
    }
    
    if (!session->initialized) {
        return VOXFORGE_ERROR_ONNX_INIT;
    }
    
    printf("Starting inference...\n");
    printf("Text: %s\n", config->text ? config->text : "No text provided");
    printf("Speaker ID: %d\n", config->speaker_id);
    printf("Output file: %s\n", config->output_path ? config->output_path : "output.wav");
    
#ifdef HAVE_ONNX_RUNTIME
    if (!session->session) {
        fprintf(stderr, "No model loaded\n");
        return VOXFORGE_ERROR_ONNX_SESSION;
    }
    
    // 首先检查模型的输入输出信息
    printf("Inspecting model inputs and outputs...\n");

    size_t num_input_nodes;
    size_t num_output_nodes;

    // 获取输入节点数量
    OrtStatus* status = session->ort_api->SessionGetInputCount(session->session, &num_input_nodes);
    if (status != NULL) {
        fprintf(stderr, "Failed to get input count\n");
        session->ort_api->ReleaseStatus(status);
        return VOXFORGE_ERROR_ONNX_INFERENCE;
    }

    // 获取输出节点数量
    status = session->ort_api->SessionGetOutputCount(session->session, &num_output_nodes);
    if (status != NULL) {
        fprintf(stderr, "Failed to get output count\n");
        session->ort_api->ReleaseStatus(status);
        return VOXFORGE_ERROR_ONNX_INFERENCE;
    }

    printf("Model has %zu inputs and %zu outputs\n", num_input_nodes, num_output_nodes);

    // 获取输入名称
    OrtAllocator* allocator;
    status = session->ort_api->GetAllocatorWithDefaultOptions(&allocator);
    if (status != NULL) {
        fprintf(stderr, "Failed to get allocator\n");
        session->ort_api->ReleaseStatus(status);
        return VOXFORGE_ERROR_ONNX_INFERENCE;
    }

    printf("Input names:\n");
    for (size_t i = 0; i < num_input_nodes; i++) {
        char* input_name;
        status = session->ort_api->SessionGetInputName(session->session, i, allocator, &input_name);
        if (status == NULL) {
            printf("  [%zu]: %s\n", i, input_name);
            allocator->Free(allocator, input_name);
        }
    }

    printf("Output names:\n");
    for (size_t i = 0; i < num_output_nodes; i++) {
        char* output_name;
        status = session->ort_api->SessionGetOutputName(session->session, i, allocator, &output_name);
        if (status == NULL) {
            printf("  [%zu]: %s\n", i, output_name);
            allocator->Free(allocator, output_name);
        }
    }

    // 加载词典和tokens
    printf("\nLoading lexicon and tokens...\n");
    VoxForgeError load_result = load_lexicon(config->lexicon_path);
    if (load_result != VOXFORGE_SUCCESS) {
        fprintf(stderr, "Failed to load lexicon\n");
        return load_result;
    }

    load_result = load_tokens(config->tokens_path);
    if (load_result != VOXFORGE_SUCCESS) {
        fprintf(stderr, "Failed to load tokens\n");
        cleanup_text_resources();
        return load_result;
    }

    // 使用正确的文本到音素转换
    printf("\nPreparing real TTS inference...\n");
    printf("Text: \"%s\"\n", config->text);

    int64_t* phoneme_ids = NULL;
    size_t phoneme_count = 0;

    VoxForgeError text_result = text_to_phoneme_ids(config->text, &phoneme_ids, &phoneme_count);
    if (text_result != VOXFORGE_SUCCESS) {
        fprintf(stderr, "Failed to convert text to phoneme IDs\n");
        cleanup_text_resources();
        return text_result;
    }

    // 准备ONNX输入
    // 输入1: x (phoneme sequence)
    int64_t x_shape[] = {1, (int64_t)phoneme_count};
    OrtValue* x_tensor = NULL;
    status = session->ort_api->CreateTensorWithDataAsOrtValue(
        session->memory_info,
        phoneme_ids,
        phoneme_count * sizeof(int64_t),
        x_shape,
        2,
        ONNX_TENSOR_ELEMENT_DATA_TYPE_INT64,
        &x_tensor
    );

    if (status != NULL) {
        fprintf(stderr, "Failed to create x tensor\n");
        session->ort_api->ReleaseStatus(status);
        free(phoneme_ids);
        return VOXFORGE_ERROR_ONNX_INFERENCE;
    }

    // 输入2: x_length
    int64_t x_length_value = (int64_t)phoneme_count;
    int64_t x_length_shape[] = {1};
    OrtValue* x_length_tensor = NULL;
    status = session->ort_api->CreateTensorWithDataAsOrtValue(
        session->memory_info,
        &x_length_value,
        sizeof(int64_t),
        x_length_shape,
        1,
        ONNX_TENSOR_ELEMENT_DATA_TYPE_INT64,
        &x_length_tensor
    );

    if (status != NULL) {
        fprintf(stderr, "Failed to create x_length tensor\n");
        session->ort_api->ReleaseStatus(status);
        session->ort_api->ReleaseValue(x_tensor);
        free(phoneme_ids);
        return VOXFORGE_ERROR_ONNX_INFERENCE;
    }

    // 输入3: noise_scale
    float noise_scale = 0.667f;
    int64_t noise_scale_shape[] = {1};
    OrtValue* noise_scale_tensor = NULL;
    status = session->ort_api->CreateTensorWithDataAsOrtValue(
        session->memory_info,
        &noise_scale,
        sizeof(float),
        noise_scale_shape,
        1,
        ONNX_TENSOR_ELEMENT_DATA_TYPE_FLOAT,
        &noise_scale_tensor
    );

    if (status != NULL) {
        fprintf(stderr, "Failed to create noise_scale tensor\n");
        session->ort_api->ReleaseStatus(status);
        session->ort_api->ReleaseValue(x_tensor);
        session->ort_api->ReleaseValue(x_length_tensor);
        free(phoneme_ids);
        return VOXFORGE_ERROR_ONNX_INFERENCE;
    }

    // 输入4: length_scale
    float length_scale = 1.0f;
    int64_t length_scale_shape[] = {1};
    OrtValue* length_scale_tensor = NULL;
    status = session->ort_api->CreateTensorWithDataAsOrtValue(
        session->memory_info,
        &length_scale,
        sizeof(float),
        length_scale_shape,
        1,
        ONNX_TENSOR_ELEMENT_DATA_TYPE_FLOAT,
        &length_scale_tensor
    );

    if (status != NULL) {
        fprintf(stderr, "Failed to create length_scale tensor\n");
        session->ort_api->ReleaseStatus(status);
        session->ort_api->ReleaseValue(x_tensor);
        session->ort_api->ReleaseValue(x_length_tensor);
        session->ort_api->ReleaseValue(noise_scale_tensor);
        free(phoneme_ids);
        return VOXFORGE_ERROR_ONNX_INFERENCE;
    }

    // 输入5: noise_scale_w
    float noise_scale_w = 0.8f;
    int64_t noise_scale_w_shape[] = {1};
    OrtValue* noise_scale_w_tensor = NULL;
    status = session->ort_api->CreateTensorWithDataAsOrtValue(
        session->memory_info,
        &noise_scale_w,
        sizeof(float),
        noise_scale_w_shape,
        1,
        ONNX_TENSOR_ELEMENT_DATA_TYPE_FLOAT,
        &noise_scale_w_tensor
    );

    if (status != NULL) {
        fprintf(stderr, "Failed to create noise_scale_w tensor\n");
        session->ort_api->ReleaseStatus(status);
        session->ort_api->ReleaseValue(x_tensor);
        session->ort_api->ReleaseValue(x_length_tensor);
        session->ort_api->ReleaseValue(noise_scale_tensor);
        session->ort_api->ReleaseValue(length_scale_tensor);
        free(phoneme_ids);
        return VOXFORGE_ERROR_ONNX_INFERENCE;
    }

    // 输入6: sid (speaker ID)
    int64_t sid_value = (int64_t)config->speaker_id;
    int64_t sid_shape[] = {1};
    OrtValue* sid_tensor = NULL;
    status = session->ort_api->CreateTensorWithDataAsOrtValue(
        session->memory_info,
        &sid_value,
        sizeof(int64_t),
        sid_shape,
        1,
        ONNX_TENSOR_ELEMENT_DATA_TYPE_INT64,
        &sid_tensor
    );

    if (status != NULL) {
        fprintf(stderr, "Failed to create sid tensor\n");
        session->ort_api->ReleaseStatus(status);
        session->ort_api->ReleaseValue(x_tensor);
        session->ort_api->ReleaseValue(x_length_tensor);
        session->ort_api->ReleaseValue(noise_scale_tensor);
        session->ort_api->ReleaseValue(length_scale_tensor);
        session->ort_api->ReleaseValue(noise_scale_w_tensor);
        free(phoneme_ids);
        return VOXFORGE_ERROR_ONNX_INFERENCE;
    }

    // 执行推理
    const char* input_names[] = {"x", "x_length", "noise_scale", "length_scale", "noise_scale_w", "sid"};
    const char* output_names[] = {"y"};
    const OrtValue* inputs[] = {x_tensor, x_length_tensor, noise_scale_tensor, length_scale_tensor, noise_scale_w_tensor, sid_tensor};
    OrtValue* outputs[] = {NULL};

    printf("Running ONNX inference with real inputs...\n");

    status = session->ort_api->Run(
        session->session,
        NULL,
        input_names,
        inputs,
        4,
        output_names,
        1,
        outputs
    );

    if (status != NULL) {
        fprintf(stderr, "ONNX inference failed\n");
        session->ort_api->ReleaseStatus(status);
        // 清理所有资源
        session->ort_api->ReleaseValue(x_tensor);
        session->ort_api->ReleaseValue(x_length_tensor);
        session->ort_api->ReleaseValue(noise_scale_tensor);
        session->ort_api->ReleaseValue(length_scale_tensor);
        free(phoneme_ids);
        return VOXFORGE_ERROR_ONNX_INFERENCE;
    }

    printf("ONNX inference completed successfully!\n");

    // 加载HiFi-GAN声码器
    VoxForgeError vocoder_result = load_hifigan_vocoder(session, "onnx_lib/hifigan_v2.onnx");
    if (vocoder_result != VOXFORGE_SUCCESS) {
        fprintf(stderr, "Failed to load HiFi-GAN vocoder\n");
        // 清理资源并返回
        session->ort_api->ReleaseValue(x_tensor);
        session->ort_api->ReleaseValue(x_length_tensor);
        session->ort_api->ReleaseValue(noise_scale_tensor);
        session->ort_api->ReleaseValue(length_scale_tensor);
        if (outputs[0]) {
            session->ort_api->ReleaseValue(outputs[0]);
        }
        free(phoneme_ids);
        cleanup_text_resources();
        return vocoder_result;
    }

    // 获取梅尔频谱图输出
    float* mel_data = NULL;
    status = session->ort_api->GetTensorMutableData(outputs[0], (void**)&mel_data);
    if (status != NULL) {
        fprintf(stderr, "Failed to get mel output data\n");
        session->ort_api->ReleaseStatus(status);
    } else {
        // 获取输出形状
        OrtTensorTypeAndShapeInfo* shape_info;
        session->ort_api->GetTensorTypeAndShape(outputs[0], &shape_info);

        size_t dim_count;
        session->ort_api->GetDimensionsCount(shape_info, &dim_count);

        int64_t* dims = malloc(dim_count * sizeof(int64_t));
        session->ort_api->GetDimensions(shape_info, dims, dim_count);

        printf("Generated mel spectrogram: ");
        for (size_t i = 0; i < dim_count; i++) {
            printf("%lld ", dims[i]);
        }
        printf("\n");

        // 简单的梅尔频谱图到音频转换（这里需要vocoder，暂时用简化方法）
        size_t mel_frames = dims[dim_count-1]; // 时间帧数
        size_t mel_bins = dims[dim_count-2];   // 梅尔频率bins

        // 使用HiFi-GAN声码器转换梅尔频谱图为音频
        float* audio_data = NULL;
        size_t audio_length = 0;

        VoxForgeError hifigan_result = hifigan_mel_to_audio(session, mel_data,
                                                           mel_frames, mel_bins,
                                                           &audio_data, &audio_length);

        if (hifigan_result == VOXFORGE_SUCCESS && audio_data) {
            printf("HiFi-GAN conversion successful: %zu samples\n", audio_length);

            // 保存音频
            VoxForgeError result = save_audio_to_file(audio_data, audio_length, config->output_path);
            free(audio_data);

            if (result == VOXFORGE_SUCCESS) {
                printf("High-quality TTS audio saved to: %s\n", config->output_path);
                printf("Generated using HiFi-GAN vocoder!\n");
            } else {
                fprintf(stderr, "Failed to save audio file\n");
            }
        } else {
            fprintf(stderr, "HiFi-GAN conversion failed\n");
        }

        free(dims);
        session->ort_api->ReleaseTensorTypeAndShapeInfo(shape_info);
    }

    // 清理所有资源
    session->ort_api->ReleaseValue(x_tensor);
    session->ort_api->ReleaseValue(x_length_tensor);
    session->ort_api->ReleaseValue(noise_scale_tensor);
    session->ort_api->ReleaseValue(length_scale_tensor);
    if (outputs[0]) {
        session->ort_api->ReleaseValue(outputs[0]);
    }
    free(phoneme_ids);
    cleanup_text_resources();

    return VOXFORGE_SUCCESS;
#else
    printf("Debug mode: Simulating inference\n");
    printf("Would process text: '%s'\n", config->text);
    printf("Would use speaker ID: %d\n", config->speaker_id);
    printf("Would save audio to: %s\n", config->output_path);
    return VOXFORGE_SUCCESS;
#endif
}

void voxforge_cleanup(VoxForgeSession* session) {
    if (!session) {
        return;
    }
    
#ifdef HAVE_ONNX_RUNTIME
    if (session->session) {
        session->ort_api->ReleaseSession(session->session);
        session->session = NULL;
    }
    
    if (session->memory_info) {
        session->ort_api->ReleaseMemoryInfo(session->memory_info);
        session->memory_info = NULL;
    }
    
    if (session->session_options) {
        session->ort_api->ReleaseSessionOptions(session->session_options);
        session->session_options = NULL;
    }
    
    if (session->env) {
        session->ort_api->ReleaseEnv(session->env);
        session->env = NULL;
    }
#endif
    
    if (session->vocoder_session) {
        session->ort_api->ReleaseSession(session->vocoder_session);
        session->vocoder_session = NULL;
    }

    session->initialized = false;
    printf("VoxForge session cleaned up\n");
}

VoxForgeError load_hifigan_vocoder(VoxForgeSession* session, const char* vocoder_path) {
    if (!session || !vocoder_path) {
        return VOXFORGE_ERROR_INVALID_ARGS;
    }

    if (!session->initialized) {
        return VOXFORGE_ERROR_ONNX_INIT;
    }

    if (!file_exists(vocoder_path)) {
        fprintf(stderr, "HiFi-GAN vocoder file not found: %s\n", vocoder_path);
        return VOXFORGE_ERROR_FILE_NOT_FOUND;
    }

#ifdef HAVE_ONNX_RUNTIME
    printf("Loading HiFi-GAN vocoder: %s\n", vocoder_path);

    OrtStatus* status = session->ort_api->CreateSession(session->env, vocoder_path,
                                                       session->session_options,
                                                       &session->vocoder_session);
    if (status != NULL) {
        fprintf(stderr, "Failed to load HiFi-GAN vocoder: %s\n", vocoder_path);
        session->ort_api->ReleaseStatus(status);
        return VOXFORGE_ERROR_ONNX_SESSION;
    }

    printf("HiFi-GAN vocoder loaded successfully\n");
    return VOXFORGE_SUCCESS;
#else
    printf("Debug mode: Would load HiFi-GAN vocoder %s\n", vocoder_path);
    return VOXFORGE_SUCCESS;
#endif
}

VoxForgeError hifigan_mel_to_audio(VoxForgeSession* session, const float* mel_data,
                                   size_t mel_frames, size_t mel_bins,
                                   float** audio_data, size_t* audio_length) {
    if (!session || !mel_data || !audio_data || !audio_length) {
        return VOXFORGE_ERROR_INVALID_ARGS;
    }

#ifdef HAVE_ONNX_RUNTIME
    if (!session->vocoder_session) {
        fprintf(stderr, "HiFi-GAN vocoder not loaded\n");
        return VOXFORGE_ERROR_ONNX_SESSION;
    }

    printf("Converting mel spectrogram to audio using HiFi-GAN...\n");
    printf("Input mel shape: [1, %zu, %zu]\n", mel_bins, mel_frames);

    // 准备输入张量 (batch_size=1, mel_bins=80, time_frames)
    int64_t mel_shape[] = {1, (int64_t)mel_bins, (int64_t)mel_frames};
    OrtValue* mel_tensor = NULL;

    OrtStatus* status = session->ort_api->CreateTensorWithDataAsOrtValue(
        session->memory_info,
        (void*)mel_data,
        mel_frames * mel_bins * sizeof(float),
        mel_shape,
        3,
        ONNX_TENSOR_ELEMENT_DATA_TYPE_FLOAT,
        &mel_tensor
    );

    if (status != NULL) {
        fprintf(stderr, "Failed to create mel tensor for HiFi-GAN\n");
        session->ort_api->ReleaseStatus(status);
        return VOXFORGE_ERROR_ONNX_INFERENCE;
    }

    // 执行HiFi-GAN推理
    const char* input_names[] = {"mel"};
    const char* output_names[] = {"audio"};
    const OrtValue* inputs[] = {mel_tensor};
    OrtValue* outputs[] = {NULL};

    status = session->ort_api->Run(
        session->vocoder_session,
        NULL,
        input_names,
        inputs,
        1,
        output_names,
        1,
        outputs
    );

    if (status != NULL) {
        fprintf(stderr, "HiFi-GAN inference failed\n");
        session->ort_api->ReleaseStatus(status);
        session->ort_api->ReleaseValue(mel_tensor);
        return VOXFORGE_ERROR_ONNX_INFERENCE;
    }

    printf("HiFi-GAN inference completed successfully!\n");

    // 获取音频输出
    float* hifigan_audio = NULL;
    status = session->ort_api->GetTensorMutableData(outputs[0], (void**)&hifigan_audio);
    if (status != NULL) {
        fprintf(stderr, "Failed to get HiFi-GAN audio output\n");
        session->ort_api->ReleaseStatus(status);
        session->ort_api->ReleaseValue(mel_tensor);
        session->ort_api->ReleaseValue(outputs[0]);
        return VOXFORGE_ERROR_ONNX_INFERENCE;
    }

    // 获取输出形状
    OrtTensorTypeAndShapeInfo* shape_info;
    session->ort_api->GetTensorTypeAndShape(outputs[0], &shape_info);

    size_t dim_count;
    session->ort_api->GetDimensionsCount(shape_info, &dim_count);

    int64_t* dims = malloc(dim_count * sizeof(int64_t));
    session->ort_api->GetDimensions(shape_info, dims, dim_count);

    size_t total_samples = 1;
    for (size_t i = 0; i < dim_count; i++) {
        total_samples *= dims[i];
    }

    printf("HiFi-GAN generated %zu audio samples\n", total_samples);

    // 复制音频数据
    *audio_data = malloc(total_samples * sizeof(float));
    if (!*audio_data) {
        free(dims);
        session->ort_api->ReleaseTensorTypeAndShapeInfo(shape_info);
        session->ort_api->ReleaseValue(mel_tensor);
        session->ort_api->ReleaseValue(outputs[0]);
        return VOXFORGE_ERROR_MEMORY;
    }

    memcpy(*audio_data, hifigan_audio, total_samples * sizeof(float));
    *audio_length = total_samples;

    // 清理资源
    free(dims);
    session->ort_api->ReleaseTensorTypeAndShapeInfo(shape_info);
    session->ort_api->ReleaseValue(mel_tensor);
    session->ort_api->ReleaseValue(outputs[0]);

    return VOXFORGE_SUCCESS;
#else
    printf("Debug mode: Would use HiFi-GAN to convert mel to audio\n");
    return VOXFORGE_SUCCESS;
#endif
}

VoxForgeError save_audio_to_file(const float* audio_data, size_t length, const char* filename) {
    if (!audio_data || !filename || length == 0) {
        return VOXFORGE_ERROR_INVALID_ARGS;
    }

    FILE* file = fopen(filename, "wb");
    if (!file) {
        fprintf(stderr, "Failed to open file for writing: %s\n", filename);
        return VOXFORGE_ERROR_FILE_NOT_FOUND;
    }

    // WAV 文件头
    const int sample_rate = 22050;  // 常用的TTS采样率
    const int channels = 1;         // 单声道
    const int bits_per_sample = 16; // 16位
    const int byte_rate = sample_rate * channels * bits_per_sample / 8;
    const int block_align = channels * bits_per_sample / 8;
    const int data_size = length * sizeof(int16_t);
    const int file_size = 36 + data_size;

    // 写入WAV头
    fwrite("RIFF", 1, 4, file);
    fwrite(&file_size, 4, 1, file);
    fwrite("WAVE", 1, 4, file);
    fwrite("fmt ", 1, 4, file);

    int fmt_size = 16;
    short audio_format = 1; // PCM
    fwrite(&fmt_size, 4, 1, file);
    fwrite(&audio_format, 2, 1, file);
    fwrite(&channels, 2, 1, file);
    fwrite(&sample_rate, 4, 1, file);
    fwrite(&byte_rate, 4, 1, file);
    fwrite(&block_align, 2, 1, file);
    fwrite(&bits_per_sample, 2, 1, file);

    fwrite("data", 1, 4, file);
    fwrite(&data_size, 4, 1, file);

    // 转换float音频数据为16位整数并写入
    for (size_t i = 0; i < length; i++) {
        // 将float [-1.0, 1.0] 转换为 int16 [-32768, 32767]
        float sample = audio_data[i];
        if (sample > 1.0f) sample = 1.0f;
        if (sample < -1.0f) sample = -1.0f;

        int16_t pcm_sample = (int16_t)(sample * 32767.0f);
        fwrite(&pcm_sample, sizeof(int16_t), 1, file);
    }

    fclose(file);
    printf("WAV file saved: %s (%zu samples, %d Hz)\n", filename, length, sample_rate);
    return VOXFORGE_SUCCESS;
}
