cmake_minimum_required(VERSION 3.16)
project(VoxForge C)

set(CMAKE_C_STANDARD 11)

# 设置编译选项
if(CMAKE_BUILD_TYPE STREQUAL "Release")
    set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -O3 -DNDEBUG")
endif()

# 检测操作系统
if(APPLE)
    message(STATUS "Building on macOS")
    set(ONNX_RUNTIME_ROOT_PATH "${CMAKE_CURRENT_SOURCE_DIR}/third_party/onnxruntime-osx-universal2-1.16.3")
elseif(UNIX AND NOT APPLE)
    message(STATUS "Building on Linux")
    set(ONNX_RUNTIME_ROOT_PATH "${CMAKE_CURRENT_SOURCE_DIR}/third_party/onnxruntime-linux-x64-1.16.3")
endif()

# 查找 ONNX Runtime
find_path(ONNX_RUNTIME_INCLUDE_DIR
    NAMES onnxruntime_c_api.h
    PATHS ${ONNX_RUNTIME_ROOT_PATH}/include
    NO_DEFAULT_PATH
)

find_library(ONNX_RUNTIME_LIB
    NAMES onnxruntime
    PATHS ${ONNX_RUNTIME_ROOT_PATH}/lib
    NO_DEFAULT_PATH
)

# 如果找不到预编译版本，尝试系统路径
if(NOT ONNX_RUNTIME_INCLUDE_DIR OR NOT ONNX_RUNTIME_LIB)
    find_path(ONNX_RUNTIME_INCLUDE_DIR NAMES onnxruntime_c_api.h)
    find_library(ONNX_RUNTIME_LIB NAMES onnxruntime)
endif()

# 添加源文件
file(GLOB_RECURSE SOURCES "src/*.c")
add_executable(VoxForge main.c ${SOURCES})

# 包含头文件目录
target_include_directories(VoxForge PRIVATE src)

# 如果找到了 ONNX Runtime
if(ONNX_RUNTIME_INCLUDE_DIR AND ONNX_RUNTIME_LIB)
    message(STATUS "Found ONNX Runtime: ${ONNX_RUNTIME_LIB}")
    target_include_directories(VoxForge PRIVATE ${ONNX_RUNTIME_INCLUDE_DIR})
    target_link_libraries(VoxForge ${ONNX_RUNTIME_LIB})
    target_compile_definitions(VoxForge PRIVATE HAVE_ONNX_RUNTIME)
else()
    message(WARNING "ONNX Runtime not found. Building without ONNX support.")
    message(STATUS "To enable ONNX Runtime, please download it to third_party/ directory")
endif()

# 静态链接选项（用于 Ubuntu 部署）
if(STATIC_BUILD)
    set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} -static")
    target_link_libraries(VoxForge -static-libgcc)
endif()
