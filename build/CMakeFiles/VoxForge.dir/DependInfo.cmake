
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/Users/<USER>/CLionProjects/VoxForge/main.c" "CMakeFiles/VoxForge.dir/main.c.o" "gcc" "CMakeFiles/VoxForge.dir/main.c.o.d"
  "/Users/<USER>/CLionProjects/VoxForge/src/onnx_inference.c" "CMakeFiles/VoxForge.dir/src/onnx_inference.c.o" "gcc" "CMakeFiles/VoxForge.dir/src/onnx_inference.c.o.d"
  "/Users/<USER>/CLionProjects/VoxForge/src/text_processor.c" "CMakeFiles/VoxForge.dir/src/text_processor.c.o" "gcc" "CMakeFiles/VoxForge.dir/src/text_processor.c.o.d"
  "/Users/<USER>/CLionProjects/VoxForge/src/utils.c" "CMakeFiles/VoxForge.dir/src/utils.c.o" "gcc" "CMakeFiles/VoxForge.dir/src/utils.c.o.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
