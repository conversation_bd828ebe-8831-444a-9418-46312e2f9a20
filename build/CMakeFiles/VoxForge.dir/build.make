# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/CLionProjects/VoxForge

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/CLionProjects/VoxForge/build

# Include any dependencies generated for this target.
include CMakeFiles/VoxForge.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/VoxForge.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/VoxForge.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/VoxForge.dir/flags.make

CMakeFiles/VoxForge.dir/codegen:
.PHONY : CMakeFiles/VoxForge.dir/codegen

CMakeFiles/VoxForge.dir/main.c.o: CMakeFiles/VoxForge.dir/flags.make
CMakeFiles/VoxForge.dir/main.c.o: /Users/<USER>/CLionProjects/VoxForge/main.c
CMakeFiles/VoxForge.dir/main.c.o: CMakeFiles/VoxForge.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/CLionProjects/VoxForge/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/VoxForge.dir/main.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/VoxForge.dir/main.c.o -MF CMakeFiles/VoxForge.dir/main.c.o.d -o CMakeFiles/VoxForge.dir/main.c.o -c /Users/<USER>/CLionProjects/VoxForge/main.c

CMakeFiles/VoxForge.dir/main.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/VoxForge.dir/main.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/CLionProjects/VoxForge/main.c > CMakeFiles/VoxForge.dir/main.c.i

CMakeFiles/VoxForge.dir/main.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/VoxForge.dir/main.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/CLionProjects/VoxForge/main.c -o CMakeFiles/VoxForge.dir/main.c.s

CMakeFiles/VoxForge.dir/src/onnx_inference.c.o: CMakeFiles/VoxForge.dir/flags.make
CMakeFiles/VoxForge.dir/src/onnx_inference.c.o: /Users/<USER>/CLionProjects/VoxForge/src/onnx_inference.c
CMakeFiles/VoxForge.dir/src/onnx_inference.c.o: CMakeFiles/VoxForge.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/CLionProjects/VoxForge/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object CMakeFiles/VoxForge.dir/src/onnx_inference.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/VoxForge.dir/src/onnx_inference.c.o -MF CMakeFiles/VoxForge.dir/src/onnx_inference.c.o.d -o CMakeFiles/VoxForge.dir/src/onnx_inference.c.o -c /Users/<USER>/CLionProjects/VoxForge/src/onnx_inference.c

CMakeFiles/VoxForge.dir/src/onnx_inference.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/VoxForge.dir/src/onnx_inference.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/CLionProjects/VoxForge/src/onnx_inference.c > CMakeFiles/VoxForge.dir/src/onnx_inference.c.i

CMakeFiles/VoxForge.dir/src/onnx_inference.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/VoxForge.dir/src/onnx_inference.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/CLionProjects/VoxForge/src/onnx_inference.c -o CMakeFiles/VoxForge.dir/src/onnx_inference.c.s

CMakeFiles/VoxForge.dir/src/text_processor.c.o: CMakeFiles/VoxForge.dir/flags.make
CMakeFiles/VoxForge.dir/src/text_processor.c.o: /Users/<USER>/CLionProjects/VoxForge/src/text_processor.c
CMakeFiles/VoxForge.dir/src/text_processor.c.o: CMakeFiles/VoxForge.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/CLionProjects/VoxForge/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object CMakeFiles/VoxForge.dir/src/text_processor.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/VoxForge.dir/src/text_processor.c.o -MF CMakeFiles/VoxForge.dir/src/text_processor.c.o.d -o CMakeFiles/VoxForge.dir/src/text_processor.c.o -c /Users/<USER>/CLionProjects/VoxForge/src/text_processor.c

CMakeFiles/VoxForge.dir/src/text_processor.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/VoxForge.dir/src/text_processor.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/CLionProjects/VoxForge/src/text_processor.c > CMakeFiles/VoxForge.dir/src/text_processor.c.i

CMakeFiles/VoxForge.dir/src/text_processor.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/VoxForge.dir/src/text_processor.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/CLionProjects/VoxForge/src/text_processor.c -o CMakeFiles/VoxForge.dir/src/text_processor.c.s

CMakeFiles/VoxForge.dir/src/utils.c.o: CMakeFiles/VoxForge.dir/flags.make
CMakeFiles/VoxForge.dir/src/utils.c.o: /Users/<USER>/CLionProjects/VoxForge/src/utils.c
CMakeFiles/VoxForge.dir/src/utils.c.o: CMakeFiles/VoxForge.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/CLionProjects/VoxForge/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building C object CMakeFiles/VoxForge.dir/src/utils.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/VoxForge.dir/src/utils.c.o -MF CMakeFiles/VoxForge.dir/src/utils.c.o.d -o CMakeFiles/VoxForge.dir/src/utils.c.o -c /Users/<USER>/CLionProjects/VoxForge/src/utils.c

CMakeFiles/VoxForge.dir/src/utils.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/VoxForge.dir/src/utils.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/CLionProjects/VoxForge/src/utils.c > CMakeFiles/VoxForge.dir/src/utils.c.i

CMakeFiles/VoxForge.dir/src/utils.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/VoxForge.dir/src/utils.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/CLionProjects/VoxForge/src/utils.c -o CMakeFiles/VoxForge.dir/src/utils.c.s

# Object files for target VoxForge
VoxForge_OBJECTS = \
"CMakeFiles/VoxForge.dir/main.c.o" \
"CMakeFiles/VoxForge.dir/src/onnx_inference.c.o" \
"CMakeFiles/VoxForge.dir/src/text_processor.c.o" \
"CMakeFiles/VoxForge.dir/src/utils.c.o"

# External object files for target VoxForge
VoxForge_EXTERNAL_OBJECTS =

VoxForge: CMakeFiles/VoxForge.dir/main.c.o
VoxForge: CMakeFiles/VoxForge.dir/src/onnx_inference.c.o
VoxForge: CMakeFiles/VoxForge.dir/src/text_processor.c.o
VoxForge: CMakeFiles/VoxForge.dir/src/utils.c.o
VoxForge: CMakeFiles/VoxForge.dir/build.make
VoxForge: /Users/<USER>/CLionProjects/VoxForge/third_party/onnxruntime-osx-universal2-1.16.3/lib/libonnxruntime.dylib
VoxForge: CMakeFiles/VoxForge.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Users/<USER>/CLionProjects/VoxForge/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Linking C executable VoxForge"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/VoxForge.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/VoxForge.dir/build: VoxForge
.PHONY : CMakeFiles/VoxForge.dir/build

CMakeFiles/VoxForge.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/VoxForge.dir/cmake_clean.cmake
.PHONY : CMakeFiles/VoxForge.dir/clean

CMakeFiles/VoxForge.dir/depend:
	cd /Users/<USER>/CLionProjects/VoxForge/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/CLionProjects/VoxForge /Users/<USER>/CLionProjects/VoxForge /Users/<USER>/CLionProjects/VoxForge/build /Users/<USER>/CLionProjects/VoxForge/build /Users/<USER>/CLionProjects/VoxForge/build/CMakeFiles/VoxForge.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/VoxForge.dir/depend

