/usr/bin/cc  -arch arm64 -Wl,-search_paths_first -Wl,-headerpad_max_install_names -L/opt/homebrew/opt/protobuf@3/lib CMakeFiles/VoxForge.dir/main.c.o CMakeFiles/VoxForge.dir/src/onnx_inference.c.o CMakeFiles/VoxForge.dir/src/text_processor.c.o CMakeFiles/VoxForge.dir/src/utils.c.o -o VoxForge  -Wl,-rpath,/Users/<USER>/CLionProjects/VoxForge/third_party/onnxruntime-osx-universal2-1.16.3/lib /Users/<USER>/CLionProjects/VoxForge/third_party/onnxruntime-osx-universal2-1.16.3/lib/libonnxruntime.dylib
