# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/CLionProjects/VoxForge

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/CLionProjects/VoxForge/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	/opt/homebrew/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/opt/homebrew/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/VoxForge/build/CMakeFiles /Users/<USER>/CLionProjects/VoxForge/build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/VoxForge/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named VoxForge

# Build rule for target.
VoxForge: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 VoxForge
.PHONY : VoxForge

# fast build rule for target.
VoxForge/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/VoxForge.dir/build.make CMakeFiles/VoxForge.dir/build
.PHONY : VoxForge/fast

main.o: main.c.o
.PHONY : main.o

# target to build an object file
main.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/VoxForge.dir/build.make CMakeFiles/VoxForge.dir/main.c.o
.PHONY : main.c.o

main.i: main.c.i
.PHONY : main.i

# target to preprocess a source file
main.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/VoxForge.dir/build.make CMakeFiles/VoxForge.dir/main.c.i
.PHONY : main.c.i

main.s: main.c.s
.PHONY : main.s

# target to generate assembly for a file
main.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/VoxForge.dir/build.make CMakeFiles/VoxForge.dir/main.c.s
.PHONY : main.c.s

src/onnx_inference.o: src/onnx_inference.c.o
.PHONY : src/onnx_inference.o

# target to build an object file
src/onnx_inference.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/VoxForge.dir/build.make CMakeFiles/VoxForge.dir/src/onnx_inference.c.o
.PHONY : src/onnx_inference.c.o

src/onnx_inference.i: src/onnx_inference.c.i
.PHONY : src/onnx_inference.i

# target to preprocess a source file
src/onnx_inference.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/VoxForge.dir/build.make CMakeFiles/VoxForge.dir/src/onnx_inference.c.i
.PHONY : src/onnx_inference.c.i

src/onnx_inference.s: src/onnx_inference.c.s
.PHONY : src/onnx_inference.s

# target to generate assembly for a file
src/onnx_inference.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/VoxForge.dir/build.make CMakeFiles/VoxForge.dir/src/onnx_inference.c.s
.PHONY : src/onnx_inference.c.s

src/text_processor.o: src/text_processor.c.o
.PHONY : src/text_processor.o

# target to build an object file
src/text_processor.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/VoxForge.dir/build.make CMakeFiles/VoxForge.dir/src/text_processor.c.o
.PHONY : src/text_processor.c.o

src/text_processor.i: src/text_processor.c.i
.PHONY : src/text_processor.i

# target to preprocess a source file
src/text_processor.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/VoxForge.dir/build.make CMakeFiles/VoxForge.dir/src/text_processor.c.i
.PHONY : src/text_processor.c.i

src/text_processor.s: src/text_processor.c.s
.PHONY : src/text_processor.s

# target to generate assembly for a file
src/text_processor.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/VoxForge.dir/build.make CMakeFiles/VoxForge.dir/src/text_processor.c.s
.PHONY : src/text_processor.c.s

src/utils.o: src/utils.c.o
.PHONY : src/utils.o

# target to build an object file
src/utils.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/VoxForge.dir/build.make CMakeFiles/VoxForge.dir/src/utils.c.o
.PHONY : src/utils.c.o

src/utils.i: src/utils.c.i
.PHONY : src/utils.i

# target to preprocess a source file
src/utils.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/VoxForge.dir/build.make CMakeFiles/VoxForge.dir/src/utils.c.i
.PHONY : src/utils.c.i

src/utils.s: src/utils.c.s
.PHONY : src/utils.s

# target to generate assembly for a file
src/utils.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/VoxForge.dir/build.make CMakeFiles/VoxForge.dir/src/utils.c.s
.PHONY : src/utils.c.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... VoxForge"
	@echo "... main.o"
	@echo "... main.i"
	@echo "... main.s"
	@echo "... src/onnx_inference.o"
	@echo "... src/onnx_inference.i"
	@echo "... src/onnx_inference.s"
	@echo "... src/text_processor.o"
	@echo "... src/text_processor.i"
	@echo "... src/text_processor.s"
	@echo "... src/utils.o"
	@echo "... src/utils.i"
	@echo "... src/utils.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

