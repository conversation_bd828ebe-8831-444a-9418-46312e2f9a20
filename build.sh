#!/bin/bash

# VoxForge 构建脚本
# 支持 macOS 开发和 Ubuntu 部署
export https_proxy=http://127.0.0.1:7890 http_proxy=http://127.0.0.1:7890 all_proxy=socks5://127.0.0.1:7890

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检测操作系统
detect_os() {
    if [[ "$OSTYPE" == "darwin"* ]]; then
        echo "macos"
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        echo "linux"
    else
        echo "unknown"
    fi
}

# 下载 ONNX Runtime
download_onnx_runtime() {
    local os=$1
    local target_dir="third_party"
    
    mkdir -p "$target_dir"
    cd "$target_dir"
    
    if [[ "$os" == "macos" ]]; then
        local onnx_file="onnxruntime-osx-universal2-1.16.3.tgz"
        local onnx_url="https://github.com/microsoft/onnxruntime/releases/download/v1.16.3/$onnx_file"
        local onnx_dir="onnxruntime-osx-universal2-1.16.3"
    elif [[ "$os" == "linux" ]]; then
        local onnx_file="onnxruntime-linux-x64-1.16.3.tgz"
        local onnx_url="https://github.com/microsoft/onnxruntime/releases/download/v1.16.3/$onnx_file"
        local onnx_dir="onnxruntime-linux-x64-1.16.3"
    else
        print_error "Unsupported OS: $os"
        return 1
    fi
    
    if [[ ! -d "$onnx_dir" ]]; then
        print_info "Downloading ONNX Runtime for $os..."
        if command -v wget >/dev/null 2>&1; then
            wget "$onnx_url"
        elif command -v curl >/dev/null 2>&1; then
            curl -L -O "$onnx_url"
        else
            print_error "Neither wget nor curl found. Please install one of them."
            return 1
        fi
        
        print_info "Extracting ONNX Runtime..."
        tar -xzf "$onnx_file"
        rm "$onnx_file"
    else
        print_info "ONNX Runtime already exists for $os"
    fi
    
    cd ..
}

# 本地构建（macOS 开发）
build_local() {
    local os=$(detect_os)
    print_info "Building locally on $os..."
    
    # 下载 ONNX Runtime
    download_onnx_runtime "$os"
    
    # 创建构建目录
    mkdir -p build
    cd build
    
    # 配置和构建
    print_info "Configuring with CMake..."
    cmake -DCMAKE_BUILD_TYPE=Debug ..
    
    print_info "Building..."
    make -j$(nproc 2>/dev/null || sysctl -n hw.ncpu 2>/dev/null || echo 4)
    
    print_info "Build completed! Executable: build/VoxForge"
    
    # 测试构建
    print_info "Testing build..."
    ./VoxForge --help || true
}

# Docker 构建（Ubuntu 静态编译）
build_docker() {
    print_info "Building with Docker for Ubuntu deployment..."
    
    # 构建 Docker 镜像
    print_info "Building Docker image..."
    docker build -t voxforge:latest .
    
    # 提取静态可执行文件
    print_info "Extracting static binary..."
    mkdir -p dist
    docker run --rm -v "$(pwd)/dist:/output" voxforge:latest cp /output/VoxForge /output/VoxForge-static
    
    # 验证静态链接
    if [[ -f "dist/VoxForge-static" ]]; then
        print_info "Static binary created: dist/VoxForge-static"
        print_info "Checking dependencies..."
        if command -v ldd >/dev/null 2>&1; then
            ldd dist/VoxForge-static || print_info "✓ Static binary - no dynamic dependencies"
        fi
        
        # 测试静态二进制文件
        print_info "Testing static binary..."
        ./dist/VoxForge-static --help || true
    else
        print_error "Failed to create static binary"
        return 1
    fi
}

# 清理
clean() {
    print_info "Cleaning build artifacts..."
    rm -rf build dist cmake-build-*
    print_info "Clean completed"
}

# 显示帮助
show_help() {
    echo "VoxForge Build Script"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  local     Build locally (for development on macOS/Linux)"
    echo "  docker    Build static binary with Docker (for Ubuntu deployment)"
    echo "  clean     Clean build artifacts"
    echo "  help      Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 local          # Build for local development"
    echo "  $0 docker         # Build static binary for deployment"
}

# 主函数
main() {
    case "${1:-local}" in
        "local")
            build_local
            ;;
        "docker")
            build_docker
            ;;
        "clean")
            clean
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            print_error "Unknown command: $1"
            show_help
            exit 1
            ;;
    esac
}

main "$@"
