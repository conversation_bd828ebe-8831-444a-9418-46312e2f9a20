#!/bin/bash

# VoxForge 说话人测试脚本
# 快速测试不同说话人的音色

MODEL_PATH="onnx_lib/vits-zh-aishell3/vits-aishell3.onnx"
DICT_DIR="onnx_lib/vits-zh-aishell3"
LEXICON="onnx_lib/vits-zh-aishell3/lexicon.txt"
TOKENS="onnx_lib/vits-zh-aishell3/tokens.txt"

# 测试文本
TEXT="你好，我是说话人测试"

# 推荐的说话人ID（基于经验选择的音质较好的）
FEMALE_SPEAKERS=(10 20 30 40 50 60 70 80 90 100 110 120)
MALE_SPEAKERS=(15 25 35 45 55 65 75 85 95 105 115 125 135 145 155 165)

echo "🎵 VoxForge 说话人测试工具"
echo "================================"
if [ -d "out" ]; then
    echo "目录 out 已存在，删除后重新创建..."
    rm -rf out
fi

mkdir -p out
# 测试女声说话人
echo "📢 测试女声说话人..."
for sid in "${FEMALE_SPEAKERS[@]}"; do
    output_file="out/speaker_female_${sid}.wav"
    echo "生成女声 SID=${sid}: ${output_file}"
    
    ./build/VoxForge \
        --vits-model="$MODEL_PATH" \
        --vits-dict-dir="$DICT_DIR" \
        --vits-lexicon="$LEXICON" \
        --vits-tokens="$TOKENS" \
        --sid="$sid" \
        --noise-scale=0.3 \           # 噪声比例
        --noise-scale-w=0.4 \         # 噪声权重
        --length-scale=1.5 \          # 语速控制
        --temperature=1.0 \           # 温度参数
        --filter-strength=0.5 \       # 滤波强度
        --output-filename="$output_file" \
        "$TEXT" > /dev/null 2>&1
    
    if [ $? -eq 0 ]; then
        echo "✅ 成功生成: $output_file"
    else
        echo "❌ 生成失败: SID=$sid"
    fi
done

echo ""
echo "📢 测试男声说话人..."
for sid in "${MALE_SPEAKERS[@]}"; do
    output_file="out/speaker_male_${sid}.wav"
    echo "生成男声 SID=${sid}: ${output_file}"
    
    ./build/VoxForge \
        --vits-model="$MODEL_PATH" \
        --vits-dict-dir="$DICT_DIR" \
        --vits-lexicon="$LEXICON" \
        --vits-tokens="$TOKENS" \
        --sid="$sid" \
        --noise-scale=0.3 \           # 噪声比例
        --noise-scale-w=0.4 \         # 噪声权重
        --length-scale=1.5 \          # 语速控制
        --temperature=1.0 \           # 温度参数
        --filter-strength=0.5 \       # 滤波强度
        --output-filename="$output_file" \
        "$TEXT" > /dev/null 2>&1
    
    if [ $? -eq 0 ]; then
        echo "✅ 成功生成: $output_file"
    else
        echo "❌ 生成失败: SID=$sid"
    fi
done

echo ""
echo "🎉 说话人测试完成！"
echo "生成的文件："
ls -la out/speaker_*.wav | head -10
echo "..."
echo "总计: $(ls out/speaker_*.wav | wc -l) 个音频文件"
echo ""
echo "💡 使用建议："
echo "1. 试听这些文件，找到您喜欢的音色"
echo "2. 记下对应的SID号码"
echo "3. 在正式使用时指定该SID"
echo ""
echo "🔧 参数调优："
echo "- 声音太尖锐：降低 --noise-scale 和 --noise-scale-w"
echo "- 语速太快：增加 --length-scale"
echo "- 声音太平：适当增加 --noise-scale-w"
