#!/bin/bash

# VoxForge 测试脚本

export https_proxy=http://127.0.0.1:7890 http_proxy=http://127.0.0.1:7890 all_proxy=socks5://127.0.0.1:7890
set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 颜色输出
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

print_info() {
    echo -e "${GREEN}[TEST]${NC} $1"
}

print_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 测试基本功能
test_basic() {
    print_info "Testing basic functionality..."
    
    local binary="./build/VoxForge"
    if [[ ! -f "$binary" ]]; then
        print_error "Binary not found: $binary"
        print_info "Please run './build.sh local' first"
        return 1
    fi
    
    # 测试帮助信息
    print_info "Testing help output..."
    $binary --help
    
    # 测试无参数调用
    print_info "Testing no arguments..."
    $binary || true
    
    # 测试调试模式（无 ONNX Runtime）
    print_info "Testing debug mode..."
    $binary --vits-model=dummy.onnx --sid=1 "Hello World" || true
}

# 测试静态二进制文件
test_static() {
    print_info "Testing static binary..."
    
    local binary="./dist/VoxForge-static"
    if [[ ! -f "$binary" ]]; then
        print_error "Static binary not found: $binary"
        print_info "Please run './build.sh docker' first"
        return 1
    fi
    
    # 测试帮助信息
    print_info "Testing static binary help output..."
    $binary --help
    
    # 测试调试模式
    print_info "Testing static binary debug mode..."
    $binary --vits-model=dummy.onnx --sid=2 "Static test" || true
}

# 主函数
main() {
    case "${1:-basic}" in
        "basic")
            test_basic
            ;;
        "static")
            test_static
            ;;
        "all")
            test_basic
            echo ""
            test_static
            ;;
        *)
            echo "Usage: $0 [basic|static|all]"
            exit 1
            ;;
    esac
    
    print_info "Tests completed!"
}

main "$@"
