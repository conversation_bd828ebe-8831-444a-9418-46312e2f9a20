#!/bin/bash

# VoxForge 使用示例

set -e

echo "VoxForge 使用示例"
echo "=================="

# 检查是否已构建
if [[ ! -f "build/VoxForge" ]]; then
    echo "❌ 未找到可执行文件，请先运行: ./build.sh local"
    exit 1
fi

echo "✅ 找到可执行文件: build/VoxForge"
echo ""

# 创建虚拟模型文件（用于演示）
touch dummy.onnx
echo "📁 创建虚拟模型文件: dummy.onnx"
echo ""

# 示例 1: 显示帮助信息
echo "📖 示例 1: 显示帮助信息"
echo "命令: ./build/VoxForge --help"
echo "----------------------------------------"
./build/VoxForge --help
echo ""

# 示例 2: 基本语音合成
echo "🎵 示例 2: 基本语音合成"
echo "命令: ./build/VoxForge --vits-model=dummy.onnx --sid=0 \"Hello World\""
echo "----------------------------------------"
./build/VoxForge --vits-model=dummy.onnx --sid=0 "Hello World"
echo ""

# 示例 3: 中文语音合成
echo "🎵 示例 3: 中文语音合成"
echo "命令: ./build/VoxForge --vits-model=dummy.onnx --sid=2 --output-filename=chinese.wav \"这是中文语音合成示例\""
echo "----------------------------------------"
./build/VoxForge --vits-model=dummy.onnx --sid=2 --output-filename=chinese.wav "这是中文语音合成示例"
echo ""

# 示例 4: 详细输出模式
echo "🔍 示例 4: 详细输出模式"
echo "命令: ./build/VoxForge --vits-model=dummy.onnx --sid=1 --verbose \"Verbose mode example\""
echo "----------------------------------------"
./build/VoxForge --vits-model=dummy.onnx --sid=1 --verbose "Verbose mode example"
echo ""

# 示例 5: 类似参考项目的完整参数
echo "⚙️  示例 5: 完整参数示例"
echo "命令: ./build/VoxForge --vits-model=dummy.onnx --vits-dict-dir=./dict --vits-lexicon=./lexicon.txt --vits-tokens=./tokens.txt --sid=2 --output-filename=full_example.wav \"这是多说话人的中文合成示例。\""
echo "----------------------------------------"
./build/VoxForge \
    --vits-model=dummy.onnx \
    --vits-dict-dir=./dict \
    --vits-lexicon=./lexicon.txt \
    --vits-tokens=./tokens.txt \
    --sid=2 \
    --output-filename=full_example.wav \
    "这是多说话人的中文合成示例。"
echo ""

# 清理
rm -f dummy.onnx
echo "🧹 清理虚拟文件"
echo ""

echo "✅ 所有示例运行完成！"
echo ""
echo "💡 提示："
echo "   - 当前运行的是调试模式，没有实际的音频输出"
echo "   - 要启用真实的语音合成，需要下载 ONNX Runtime 并提供真实的 VITS 模型"
echo "   - 运行 './build.sh local' 会自动下载 ONNX Runtime"
